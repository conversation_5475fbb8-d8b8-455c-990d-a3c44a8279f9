defmodule Drops.Operations.UnitOfWorkTest do
  use Drops.OperationCase, async: true

  alias Drops.Operations.UnitOfWork

  # Helper function to create UnitOfWork with default steps
  defp new_uow(
         operation_module,
         steps \\ [:conform, :prepare, :validate, :execute]
       ) do
    UnitOfWork.new(operation_module, steps)
  end

  describe "new/2" do
    test "creates a UnitOfWork with default steps" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = new_uow(TestOperation)

      assert uow.operation_module == TestOperation
      assert uow.steps[:conform] == {TestOperation, :conform}
      assert uow.steps[:prepare] == {TestOperation, :prepare}
      assert uow.steps[:validate] == {TestOperation, :validate}
      assert uow.steps[:execute] == {TestOperation, :execute}
    end

    test "creates UnitOfWork without any default callbacks" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = new_uow(TestOperation)

      assert uow.callbacks.after == %{}
      assert uow.callbacks.before == %{}
    end
  end

  describe "after_step/3" do
    test "adds new step after existing step" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = new_uow(TestOperation)
      updated_uow = UnitOfWork.after_step(uow, :prepare, :audit)

      # Check that audit step was added after prepare
      prepare_index = Enum.find_index(updated_uow.step_order, &(&1 == :prepare))
      audit_index = Enum.find_index(updated_uow.step_order, &(&1 == :audit))

      assert audit_index == prepare_index + 1
      assert updated_uow.steps[:audit] == {TestOperation, :audit}
    end
  end

  describe "after_step/3 with non-existent step" do
    test "adds new step at end when existing step not found" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = new_uow(TestOperation)
      updated_uow = UnitOfWork.after_step(uow, :nonexistent, :audit)

      # Check that audit step was added at the end
      assert List.last(updated_uow.step_order) == :audit
      assert updated_uow.steps[:audit] == {TestOperation, :audit}
    end
  end

  describe "before_step/3" do
    test "adds new step before existing step" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = new_uow(TestOperation)
      updated_uow = UnitOfWork.before_step(uow, :execute, :audit)

      # Check that audit step was added before execute
      execute_index = Enum.find_index(updated_uow.step_order, &(&1 == :execute))
      audit_index = Enum.find_index(updated_uow.step_order, &(&1 == :audit))

      assert audit_index == execute_index - 1
      assert updated_uow.steps[:audit] == {TestOperation, :audit}
    end
  end

  describe "before_step/3 with non-existent step" do
    test "adds new step at beginning when existing step not found" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = new_uow(TestOperation)
      updated_uow = UnitOfWork.before_step(uow, :nonexistent, :audit)

      # Check that audit step was added at the beginning
      assert List.first(updated_uow.step_order) == :audit
      assert updated_uow.steps[:audit] == {TestOperation, :audit}
    end
  end

  describe "register_before_callback/5" do
    test "registers a before callback with config" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = new_uow(TestOperation)
      config = %{key: "value"}

      updated_uow =
        UnitOfWork.register_before_callback(
          uow,
          :prepare,
          TestOperation,
          :my_callback,
          config
        )

      assert {TestOperation, :my_callback, config} in updated_uow.callbacks.before[
               :prepare
             ]
    end
  end

  describe "register_after_callback/5" do
    test "registers an after callback with config" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = new_uow(TestOperation)
      config = %{key: "value"}

      updated_uow =
        UnitOfWork.register_after_callback(
          uow,
          :prepare,
          TestOperation,
          :my_callback,
          config
        )

      assert {TestOperation, :my_callback, config} in updated_uow.callbacks.after[
               :prepare
             ]
    end
  end

  describe "override_step/4" do
    test "overrides an existing step" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      defmodule CustomModule do
      end

      uow = new_uow(TestOperation)
      updated_uow = UnitOfWork.override_step(uow, :prepare, CustomModule, :custom_prepare)

      assert updated_uow.steps[:prepare] == {CustomModule, :custom_prepare}
    end
  end

  describe "process/2" do
    operation do
      schema do
        %{
          required(:name) => string()
        }
      end

      @impl true
      def execute(%{params: params}) do
        {:ok, Map.put(params, :processed, true)}
      end
    end

    test "processes through full pipeline", %{operation: operation} do
      uow = operation.__unit_of_work__()
      context = %{params: %{name: "test"}}

      {:ok, result} = UnitOfWork.process(uow, context)

      assert result == %{name: "test", processed: true}
    end

    test "processes without adding operation_module to context" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command

        def conform(context), do: {:ok, context}
        def prepare(context), do: {:ok, context}
        def validate(context), do: {:ok, context}

        def execute(context) do
          # Verify operation_module is NOT in context
          refute Map.has_key?(context, :operation_module)
          {:ok, context}
        end
      end

      uow = new_uow(TestOperation)
      context = %{params: %{}}

      {:ok, _result} = UnitOfWork.process(uow, context)
    end

    test "handles errors in pipeline" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command

        def conform(context), do: {:ok, context}
        def prepare(context), do: {:ok, context}
        def validate(_context), do: {:error, "validation failed"}
        def execute(_context), do: {:ok, %{}}
      end

      uow = new_uow(TestOperation)
      context = %{params: %{}}

      {:error, error} = UnitOfWork.process(uow, context)
      assert error == "validation failed"
    end

    test "processes correctly without conform step" do
      defmodule TestOperationNoConform do
        def schema, do: %{}
        def __operation_type__, do: :command

        def prepare(context), do: {:ok, context}
        def validate(context), do: {:ok, context}

        def execute(%{params: params}) do
          if params[:name] == nil do
            {:error, "name is required"}
          else
            {:ok, params}
          end
        end
      end

      uow = new_uow(TestOperationNoConform, [:prepare, :validate, :execute])
      context = %{params: %{name: "Jane Doe"}}

      {:ok, result} = UnitOfWork.process(uow, context)
      assert result == %{name: "Jane Doe"}
    end
  end
end
